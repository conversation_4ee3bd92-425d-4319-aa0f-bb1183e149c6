#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create gallery categories for Gondar City attractions.
This script safely creates categories for the dynamic image system.
"""

import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from gallery.models import GalleryCategory
from django.utils.text import slugify

def create_gondar_categories():
    """Create gallery categories for Gondar City attractions"""
    
    print("Creating Gondar City gallery categories...")
    
    # Define categories for Gondar attractions
    categories_data = [
        {
            'name': 'Gondar City',
            'description': 'Historic city of Gondar and its attractions',
            'slug': 'gondar-city',
            'icon': 'fas fa-city',
            'color': '#8B4513',  # Brown color for historic theme
            'is_active': True,
            'order': 10,
        },
        {
            'name': '<PERSON><PERSON><PERSON>',
            'description': 'Royal Enclosure containing six castles and several other buildings',
            'slug': 'fasil-ghebbi',
            'icon': 'fas fa-crown',
            'color': '#FFD700',  # Gold color for royal theme
            'is_active': True,
            'order': 11,
        },
        {
            'name': 'Debre Berhan Selassie',
            'description': 'Famous church known for its beautiful ceiling decorated with angelic faces',
            'slug': 'debre-berhan-selassie',
            'icon': 'fas fa-church',
            'color': '#4169E1',  # Royal blue for church theme
            'is_active': True,
            'order': 12,
        },
        {
            'name': 'Fasilides Bath',
            'description': 'Historic bathing complex used for annual Timkat celebrations',
            'slug': 'fasilides-bath',
            'icon': 'fas fa-swimming-pool',
            'color': '#20B2AA',  # Light sea green for water theme
            'is_active': True,
            'order': 13,
        },
    ]
    
    created_count = 0
    updated_count = 0
    
    for cat_data in categories_data:
        try:
            category, created = GalleryCategory.objects.get_or_create(
                slug=cat_data['slug'],
                defaults=cat_data
            )
            
            if created:
                created_count += 1
                print(f"   ✓ Created category: {category.name}")
            else:
                # Update existing category with new data
                for key, value in cat_data.items():
                    if key != 'slug':  # Don't update slug
                        setattr(category, key, value)
                category.save()
                updated_count += 1
                print(f"   • Updated existing category: {category.name}")
                
        except Exception as e:
            print(f"   ✗ Error creating/updating category {cat_data['name']}: {e}")
    
    print(f"\nGondar categories setup complete!")
    print(f"Created: {created_count} new categories")
    print(f"Updated: {updated_count} existing categories")
    
    # Display all Gondar-related categories
    print("\nCurrent Gondar-related categories:")
    gondar_categories = GalleryCategory.objects.filter(
        slug__in=['gondar-city', 'fasil-ghebbi', 'debre-berhan-selassie', 'fasilides-bath']
    ).order_by('order')
    
    for cat in gondar_categories:
        print(f"   - {cat.name} (slug: {cat.slug}, images: {cat.image_count})")

if __name__ == '__main__':
    create_gondar_categories()
