/* Modern Landing Page Styles */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #f59e0b;
  --accent-color: #10b981;
  --text-dark: #1f2937;
  --text-light: #6b7280;
  --bg-light: #f9fafb;
  --bg-white: #ffffff;
  --border-color: #e5e7eb;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  overflow-x: hidden;
}

.modern-landing {
  position: relative;
}

/* Navigation Styles */
.modern-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 99999;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  transition: all 0.3s ease;
  min-height: 85px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 0;
  min-height: 85px;
}

.navbar-brand {
  color: #ffffff;
  /* color: var(--text-dark); */
  flex: 0 0 auto;
  font-size: 1.25rem;
  font-weight: 700;
  gap: 1rem;
  /* min-width: 280px; */
  text-decoration: none;
  display: flex;
  align-items: center;
}

.brand-logo {
  width: 65px;
  height: 65px;
  object-fit: contain;
  border-radius: 8px;
}

.navbar-menu {
  display: flex !important;
  align-items: center;
  gap: 2rem;
  visibility: visible !important;
  opacity: 1 !important;
}

.nav-link {
  color: var(--text-light);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-color);
  border-radius: 1px;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-dark);
  cursor: pointer;
}

/* Enhanced Hero Section */
.hero-section-enhanced {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Background Carousel */
.hero-background-carousel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  transform: scale(1.1);
}

.background-slide.active {
  opacity: 1;
  animation: backgroundZoom 8s ease-in-out infinite alternate;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  z-index: 2;
}

@keyframes backgroundZoom {
  0% { transform: scale(1.1); }
  100% { transform: scale(1.15); }
}

/* Floating Elements */
.floating-elements-hero {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  pointer-events: none;
}

.floating-shape-hero {
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: floatHero 12s ease-in-out infinite;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes floatHero {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Content Container */
.hero-content-container {
  position: relative;
  z-index: 4;
  width: 100%;
}

.hero-content-enhanced {
  padding: 2rem 0;
}

/* Enhanced Status Badge */
.status-badge-enhanced {
  display: inline-flex;
  align-items: center;
  padding: 12px 20px;
  border-radius: 50px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: statusPulse 2s ease-in-out infinite;
}

.status-badge-enhanced.upcoming {
  background: rgba(52, 152, 219, 0.2);
  color: #3498db;
  box-shadow: 0 8px 32px rgba(52, 152, 219, 0.3);
}

.status-badge-enhanced.live {
  background: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
  box-shadow: 0 8px 32px rgba(231, 76, 60, 0.3);
}

.status-badge-enhanced.past {
  background: rgba(149, 165, 166, 0.2);
  color: #95a5a6;
  box-shadow: 0 8px 32px rgba(149, 165, 166, 0.3);
}

.status-icon-enhanced {
  margin-right: 12px;
  font-size: 1.2rem;
}

.status-content {
  display: flex;
  flex-direction: column;
}

.status-label {
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 2px;
}

.status-date {
  font-size: 0.8rem;
  opacity: 0.8;
}

@keyframes statusPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Enhanced Title */
.hero-title-enhanced h1 {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.title-decoration-enhanced {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #e74c3c);
  border-radius: 2px;
  margin: 1rem 0;
  animation: decorationGlow 3s ease-in-out infinite;
}

@keyframes decorationGlow {
  0%, 100% { box-shadow: 0 0 10px rgba(52, 152, 219, 0.5); }
  50% { box-shadow: 0 0 20px rgba(231, 76, 60, 0.5); }
}

.hero-subtitle-enhanced {
  font-size: 1.3rem;
  font-weight: 300;
  line-height: 1.4;
  opacity: 0.9;
}

.hero-description-enhanced {
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.8;
  max-width: 600px;
}

/* Enhanced Stats */
.hero-stats-enhanced {
  display: flex;
  gap: 2rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.stat-item-enhanced {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-item-enhanced:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-icon-enhanced {
  margin-right: 1rem;
  font-size: 1.5rem;
  color: #3498db;
}

.stat-content-enhanced {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
}

/* Enhanced Buttons */
.hero-actions-enhanced {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.btn-enhanced {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  overflow: hidden;
  border: none;
  cursor: pointer;
}

.btn-primary-enhanced {
  background: linear-gradient(45deg, #3498db, #e74c3c);
  color: white;
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.btn-primary-enhanced:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(52, 152, 219, 0.6);
  color: white;
}

.btn-secondary-enhanced {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.btn-secondary-enhanced:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
  color: white;
}

.btn-icon-enhanced {
  margin-right: 10px;
  font-size: 1.1rem;
}

.btn-text-enhanced {
  position: relative;
  z-index: 2;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
  border-radius: 50px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.btn-primary-enhanced:hover .btn-glow {
  opacity: 1;
}

/* Enhanced Controls */
.hero-controls-enhanced {
  position: absolute;
  bottom: 100px;
  left: 0;
  right: 0;
  z-index: 5;
}

.controls-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
}

.nav-arrow {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.nav-arrow:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.slide-indicators {
  display: flex;
  gap: 1rem;
}

.indicator {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.indicator.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.05);
}

.indicator-number {
  font-weight: 700;
  margin-right: 8px;
  font-size: 0.9rem;
}

.indicator-title {
  font-size: 0.8rem;
  opacity: 0.8;
}

/* Enhanced Scroll Indicator */
.scroll-indicator-enhanced {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 5;
}

.scroll-link-enhanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.scroll-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  animation: scrollBounce 2s ease-in-out infinite;
  backdrop-filter: blur(10px);
}

.scroll-text {
  font-size: 0.9rem;
  opacity: 0.8;
}

@keyframes scrollBounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

/* Wave Separator */
.wave-separator {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
  transform: rotate(180deg);
  z-index: 6;
}

.wave-separator svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 120px;
}

.wave-separator .shape-fill {
  fill: #ffffff;
}

/* Responsive Design for Enhanced Hero */
@media (max-width: 768px) {
  .hero-title-enhanced h1 {
    font-size: 2.5rem;
  }

  .hero-subtitle-enhanced {
    font-size: 1.1rem;
  }

  .hero-description-enhanced {
    font-size: 1rem;
  }

  .hero-stats-enhanced {
    gap: 1rem;
  }

  .stat-item-enhanced {
    padding: 0.8rem 1rem;
    flex: 1;
    min-width: 140px;
  }

  .hero-actions-enhanced {
    flex-direction: column;
    align-items: stretch;
  }

  .btn-enhanced {
    justify-content: center;
    padding: 12px 25px;
  }

  .controls-wrapper {
    flex-direction: column;
    gap: 1rem;
  }

  .slide-indicators {
    order: -1;
    justify-content: center;
  }

  .indicator-title {
    display: none;
  }

  .wave-separator svg {
    height: 80px;
  }
}

@media (max-width: 576px) {
  .hero-title-enhanced h1 {
    font-size: 2rem;
  }

  .status-badge-enhanced {
    padding: 8px 15px;
  }

  .hero-stats-enhanced {
    flex-direction: column;
    gap: 0.8rem;
  }

  .stat-item-enhanced {
    justify-content: center;
    text-align: center;
  }

  .floating-shape-hero {
    width: 40px;
    height: 40px;
  }
}

/* Celebration stars pattern */
.hero-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.4), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.5), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.4), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.3), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: starsFloat 25s linear infinite;
  z-index: 1;
}

@keyframes backgroundFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

@keyframes starsFloat {
  0% { transform: translateX(0px) translateY(0px); }
  100% { transform: translateX(-200px) translateY(-100px); }
}

.hero-slider {
  position: relative;
  width: 100%;
  height: 100%;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  opacity: 0;
  transition: opacity 1.5s ease-in-out;
  display: flex;
  align-items: center;
}

.hero-slide.active {
  opacity: 1;
}

.hero-content {
  color: white;
  max-width: 800px;
  animation: slideInUp 1s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #e0f2fe;
}

.hero-description {
  font-size: 1.125rem;
  line-height: 1.7;
  margin-bottom: 2rem;
  color: #f0f9ff;
  max-width: 600px;
}

.hero-stats {
  display: flex;
  gap: 3rem;
  margin-bottom: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  color: #f0f0f0;
  display: block;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  color: #f4faff;
  display: block;
  font-size: .875rem;
  margin-top: .25rem;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  cursor: pointer;
  font-size: 1rem;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  color: white;
}

.btn-outline-light {
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  transform: translateY(-2px);
}

.btn-lg {
  padding: 1rem 2.5rem;
  font-size: 1.125rem;
}

/* Hero Controls */
.hero-controls {
  position: absolute;
  bottom: 2rem;
  left: 0;
  right: 0;
  z-index: 10;
}

.slider-dots {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: white;
  transform: scale(1.2);
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 4rem;
  right: 2rem;
  z-index: 10;
}

.scroll-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  text-decoration: none;
  font-size: 0.875rem;
  animation: bounce 2s infinite;
}

.scroll-link i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Quick Stats Section */
.quick-stats {
  padding: 4rem 0;
  background: var(--bg-white);
  margin-top: -2rem;
  position: relative;
  z-index: 5;
  border-radius: 2rem 2rem 0 0;
}

.quick-stats .stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: white;
  border-radius: 1rem;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.quick-stats .stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 1rem;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 800;
  color: var(--text-dark);
  margin-bottom: 0.25rem;
}

.stat-content p {
  color: var(--text-light);
  font-weight: 500;
  margin: 0;
}

/* Authenticated Home Styles */
.authenticated-home {
  min-height: 100vh;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
}

.auth-hero {
  padding: 4rem 0;
}

.auth-stats .stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.auth-stats .stat-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.auth-stats .stat-card h3 {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
}

.auth-stats .stat-card p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 500;
}

/* Section Styles */
.section-header {
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--text-dark);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--text-light);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Events Section */
.events-section {
  padding: 6rem 0;
  background: var(--bg-light);
}

.events-grid {
  margin-bottom: 3rem;
}

.event-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.event-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.event-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.event-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.event-card:hover .event-image img {
  transform: scale(1.05);
}

.event-placeholder {
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3rem;
}

.event-date {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: white;
  border-radius: 0.5rem;
  padding: 0.5rem;
  text-align: center;
  box-shadow: var(--shadow-md);
}

.event-date .day {
  display: block;
  font-size: 1.25rem;
  font-weight: 800;
  color: var(--primary-color);
  line-height: 1;
}

.event-date .month {
  display: block;
  font-size: 0.75rem;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.event-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.event-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: var(--text-light);
}

.event-meta span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.event-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.event-description {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 1rem;
  flex: 1;
}

.event-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.event-stats .stat {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-light);
}

.event-actions {
  display: flex;
  gap: 0.75rem;
}

.btn-outline-primary {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Features Section */
.features-section {
  padding: 6rem 0;
  background: white;
}

.features-grid {
  margin-bottom: 3rem;
}

.feature-card {
  text-align: center;
  padding: 2rem;
  height: 100%;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  width: 80px;
  height: 80px;
  border-radius: 1rem;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  margin: 0 auto 1.5rem;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
  background: var(--gradient-secondary);
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  color: var(--text-light);
  padding: 0.25rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.feature-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-weight: bold;
}

/* Gallery Section */
.gallery-section {
  padding: 6rem 0;
  background: var(--bg-light);
}

.gallery-grid {
  margin-bottom: 3rem;
}

.gallery-item {
  height: 100%;
  border-radius: 1rem;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.gallery-image {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.gallery-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-image img {
  transform: scale(1.1);
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
  display: flex;
  align-items: flex-end;
  padding: 1.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-content {
  color: white;
}

.gallery-content h4 {
  font-size: 1.125rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.gallery-content p {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.gallery-category {
  display: inline-block;
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Responsive Design */
@media (min-width: 769px) {
  .navbar-menu {
    display: flex !important;
  }

  .mobile-menu-toggle {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .navbar-menu {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.25rem;
  }

  .hero-stats {
    gap: 1.5rem;
  }

  .hero-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .scroll-indicator {
    right: 50%;
    transform: translateX(50%);
  }

  .section-title {
    font-size: 2rem;
  }

  .event-actions {
    flex-direction: column;
  }

  .event-stats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .gallery-image {
    height: 250px;
  }
}

/* About Section */
.about-section {
  padding: 6rem 0;
  background: white;
}

.about-content {
  padding-right: 2rem;
}

.about-description {
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--text-light);
  margin-bottom: 2rem;
}

.about-stats {
  margin-bottom: 2rem;
}

.about-stat {
  text-align: center;
  padding: 1rem;
  background: var(--bg-light);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.about-stat:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-3px);
}

.about-stat h3 {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 0.25rem;
  color: var(--primary-color);
}

.about-stat:hover h3 {
  color: white;
}

.about-stat p {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-light);
}

.about-stat:hover p {
  color: rgba(255, 255, 255, 0.9);
}

.about-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.about-image {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: var(--shadow-xl);
}

.about-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.about-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: var(--secondary-color);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  box-shadow: var(--shadow-md);
}

/* CTA Section */
.cta-section {
  padding: 4rem 0;
  background: var(--gradient-primary);
  color: white;
}

.cta-content {
  text-align: center;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.cta-description {
  font-size: 1.125rem;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 0;
}

.btn-light {
  background: white;
  color: var(--primary-color);
  border: 2px solid white;
}

.btn-light:hover {
  background: transparent;
  color: white;
  border-color: white;
  transform: translateY(-2px);
}

/* Footer */
.modern-footer {
  background: var(--text-dark);
  color: white;
  padding: 4rem 0 0;
}

.footer-content {
  padding-bottom: 3rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-brand .brand-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.footer-brand .brand-logo img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.footer-description {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  border-radius: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.footer-links h4 {
  font-size: 1.125rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: white;
}

.footer-contact h4 {
  font-size: 1.125rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
}

.contact-item i {
  width: 20px;
  color: var(--primary-color);
}

.contact-item a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-item a:hover {
  color: white;
}

.footer-bottom {
  padding: 2rem 0;
}

.copyright,
.powered-by {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

/* Enhanced Campus Life Section */
.campus-life-section {
  padding: 120px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.campus-life-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.03) 0%, transparent 50%);
  pointer-events: none;
  animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
  0%, 100% { transform: translateX(0px) translateY(0px); }
  33% { transform: translateX(10px) translateY(-5px); }
  66% { transform: translateX(-5px) translateY(10px); }
}

/* Section Badge */
.section-badge {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Enhanced Campus Stats Row */
.campus-stats-row {
  margin-bottom: 5rem;
}

.modern-stat-card {
  position: relative;
  overflow: hidden;
}

.modern-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-stat-card:hover::before {
  opacity: 1;
}

.stat-trend {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.stat-trend i {
  font-size: 0.75rem;
}

.campus-stat-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.campus-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.campus-stat-card:hover::before {
  transform: scaleX(1);
}

.campus-stat-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.campus-stat-card .stat-icon {
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: white;
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.campus-stat-card .stat-content h3 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.campus-stat-card .stat-content p {
  color: var(--text-light);
  font-weight: 500;
  margin: 0;
}

/* Campus Gallery Grid */
.campus-gallery-grid {
  margin-bottom: 4rem;
}

.campus-gallery-item {
  height: 100%;
  border-radius: 24px;
  overflow: hidden;
  position: relative;
  box-shadow: var(--shadow-lg);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.campus-gallery-item.large-item {
  min-height: 400px;
}

.campus-gallery-item:not(.large-item) {
  min-height: 300px;
}

.campus-gallery-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.campus-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.campus-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease;
}

.campus-gallery-item:hover .campus-image {
  transform: scale(1.1);
}

.campus-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  display: flex;
  align-items: flex-end;
  padding: 2rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.campus-gallery-item:hover .campus-overlay {
  opacity: 1;
}

.campus-content {
  color: white;
  width: 100%;
}

.campus-category {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.campus-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.campus-description {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.campus-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  opacity: 0.8;
}

.campus-location i {
  font-size: 0.75rem;
}

/* Enhanced Campus Highlights */
.campus-highlights {
  margin-top: 5rem;
}

.campus-highlight-card {
  background: white;
  border-radius: 24px;
  padding: 2rem;
  text-align: left;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.9);
  transition: all 0.4s ease;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.campus-highlight-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.campus-highlight-card:hover::before {
  transform: scaleX(1);
}

.campus-highlight-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.highlight-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.campus-highlight-card .feature-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.campus-highlight-card:hover .feature-icon {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.highlight-badge {
  position: relative;
}

.highlight-badge .badge {
  font-size: 0.75rem;
  padding: 0.4rem 0.8rem;
  border-radius: 12px;
  font-weight: 600;
}

.campus-highlight-card h4 {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
  line-height: 1.3;
}

.campus-highlight-card p {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.highlight-stats {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.highlight-stats strong {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--text-dark);
}

.highlight-stats small {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 0.25rem;
}

.highlight-action {
  margin-top: auto;
}

.highlight-action .btn {
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.6rem 1.2rem;
  transition: all 0.3s ease;
}

.highlight-action .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

/* Campus CTA Buttons */
.campus-cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.campus-cta-buttons .btn {
  padding: 1rem 2rem;
  border-radius: 16px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.campus-cta-buttons .btn-primary {
  background: var(--gradient-primary);
  border: none;
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.campus-cta-buttons .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.4);
}

.campus-cta-buttons .btn-outline-primary {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  background: white;
}

.campus-cta-buttons .btn-outline-primary:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(37, 99, 235, 0.3);
}

/* Enhanced Campus CTA Section */
.campus-cta-section {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  border-radius: 24px;
  padding: 3rem 2rem;
  margin-top: 4rem;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.campus-cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.cta-content {
  position: relative;
  z-index: 2;
}

.cta-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cta-subtitle {
  font-size: 1.1rem;
  color: var(--text-light);
  margin-bottom: 2rem;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-note {
  opacity: 0.8;
  font-style: italic;
  color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .stat-item {
    text-align: center;
  }

  .events-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    text-align: center;
  }

  .footer-links ul {
    justify-content: center;
  }

  /* Campus Life Mobile Styles */
  .campus-stats-row .row {
    gap: 1rem;
  }

  .campus-gallery-grid .row {
    gap: 1.5rem;
  }

  .campus-gallery-item.large-item {
    min-height: 250px;
  }

  .campus-gallery-item:not(.large-item) {
    min-height: 200px;
  }

  .campus-overlay {
    padding: 1.5rem;
  }

  .campus-title {
    font-size: 1.25rem;
  }

  .campus-description {
    font-size: 0.875rem;
  }

  .campus-highlight-card {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .highlight-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .campus-cta-section {
    padding: 2rem 1.5rem;
    margin-top: 3rem;
  }

  .cta-title {
    font-size: 1.75rem;
  }

  .cta-subtitle {
    font-size: 1rem;
  }

  .campus-cta-buttons {
    flex-direction: column;
    width: 100%;
    gap: 0.75rem;
  }

  .campus-cta-buttons .btn {
    width: 100%;
    max-width: 300px;
  }

  .section-badge {
    font-size: 0.8rem;
    padding: 6px 16px;
  }
}

/* Campus Life Slider Styles */
.campus-life-slider-container {
  position: relative;
  width: 100%;
  background: #000;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: var(--shadow-xl);
}

.campus-slider-main {
  position: relative;
  height: 80vh;
  min-height: 600px;
  overflow: hidden;
}

.slider-viewport {
  position: relative;
  width: 100%;
  height: 100%;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 1;
}

.slide.active {
  opacity: 1;
  transform: translateX(0);
  z-index: 2;
}

.slide.next {
  transform: translateX(100%);
}

.slide.prev {
  transform: translateX(-100%);
}

.slide-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.8s ease;
}

.slide.active .slide-image {
  transform: scale(1.05);
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  z-index: 2;
}

.slide-content {
  position: relative;
  z-index: 3;
  height: 100%;
  display: flex;
  align-items: center;
}

.slide-info {
  color: white;
  max-width: 600px;
}

.slide-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.slide-category {
  background: var(--gradient-primary);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 25px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.slide-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
}

.slide-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.slide-description {
  font-size: 1.25rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
}

.slide-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.slide-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.slide-actions .btn {
  padding: 1rem 2rem;
  border-radius: 16px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.slide-actions .btn-primary {
  background: var(--gradient-primary);
  border-color: transparent;
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.slide-actions .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.4);
}

.slide-actions .btn-outline-light {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
}

.slide-actions .btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.slide-credits {
  display: flex;
  gap: 2rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.slide-credits span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.slide-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.visual-frame {
  position: relative;
  max-width: 500px;
  width: 100%;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
  transition: transform 0.3s ease;
}

.visual-frame:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg) scale(1.05);
}

.featured-image {
  width: 100%;
  height: auto;
  display: block;
}

.featured-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--gradient-secondary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Slider Controls */
.slider-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  z-index: 10;
  pointer-events: none;
}

.control-btn {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: white;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  pointer-events: auto;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
}

.play-pause-btn {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 50px;
}

/* Progress Bar */
.slider-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  z-index: 10;
}

.progress-bar {
  height: 100%;
  background: var(--gradient-primary);
  transition: width 0.3s ease;
}

/* Thumbnails */
.slider-thumbnails {
  background: rgba(0, 0, 0, 0.9);
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.thumbnails-container {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding: 0.5rem 0;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.thumbnails-container::-webkit-scrollbar {
  height: 4px;
}

.thumbnails-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.thumbnails-container::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 2px;
}

.thumbnail {
  position: relative;
  flex-shrink: 0;
  width: 120px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  background: none;
  padding: 0;
}

.thumbnail:hover {
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

.thumbnail.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 0.5rem;
  color: white;
}

.thumbnail-title {
  display: block;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

.thumbnail-category {
  display: block;
  font-size: 0.625rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Slide Counter */
.slide-counter {
  position: absolute;
  top: 2rem;
  right: 2rem;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  color: white;
  font-family: 'Courier New', monospace;
  font-size: 1rem;
  font-weight: 600;
  z-index: 10;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.slide-counter .current {
  color: var(--primary-color);
}

.slide-counter .separator {
  margin: 0 0.5rem;
  color: rgba(255, 255, 255, 0.5);
}

.slide-counter .total {
  color: rgba(255, 255, 255, 0.7);
}

/* Empty State */
.campus-slider-empty {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-light);
  border-radius: 24px;
  border: 2px dashed var(--border-color);
}

.empty-state {
  text-align: center;
  max-width: 400px;
}

/* Responsive Design for Slider */
@media (max-width: 768px) {
  .campus-slider-main {
    height: 60vh;
    min-height: 500px;
  }

  .slide-title {
    font-size: 2.5rem;
  }

  .slide-description {
    font-size: 1rem;
  }

  .slide-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-item {
    padding: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .slide-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .slide-actions .btn {
    width: 100%;
    padding: 0.875rem 1.5rem;
  }

  .slider-controls {
    padding: 0 1rem;
  }

  .control-btn {
    width: 50px;
    height: 50px;
    font-size: 1rem;
  }

  .visual-frame {
    max-width: 300px;
    transform: none;
  }

  .visual-frame:hover {
    transform: scale(1.02);
  }

  .thumbnail {
    width: 100px;
    height: 70px;
  }

  .slide-counter {
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* Modern Hero Slider Styles */
.hero-slide-modern {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  z-index: 2;
}

/* Add professional geometric patterns */
.hero-slide-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(45deg, rgba(255, 255, 255, 0.03) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(255, 255, 255, 0.03) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.03) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.03) 75%);
  background-size: 60px 60px;
  background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
  animation: patternMove 30s linear infinite;
  z-index: 1;
}

/* Celebration particles */
.hero-slide-modern::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(3px 3px at 30px 50px, rgba(255, 215, 0, 0.6), transparent),
    radial-gradient(2px 2px at 80px 30px, rgba(255, 255, 255, 0.8), transparent),
    radial-gradient(1px 1px at 120px 80px, rgba(255, 215, 0, 0.7), transparent),
    radial-gradient(2px 2px at 200px 40px, rgba(255, 255, 255, 0.6), transparent),
    radial-gradient(1px 1px at 250px 90px, rgba(255, 215, 0, 0.5), transparent),
    radial-gradient(3px 3px at 300px 20px, rgba(255, 255, 255, 0.7), transparent);
  background-repeat: repeat;
  background-size: 350px 120px;
  animation: celebrationFloat 20s ease-in-out infinite;
  z-index: 1;
}

@keyframes patternMove {
  0% { transform: translateX(0px) translateY(0px); }
  100% { transform: translateX(60px) translateY(60px); }
}

@keyframes celebrationFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-20px) translateX(10px) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) translateX(-5px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) translateX(15px) rotate(270deg);
    opacity: 1;
  }
}

.hero-slide-modern.active {
  animation: slideIn 0.8s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Left Content Section */
.hero-content-modern {
  height: 100vh;
  display: flex;
  align-items: center;
  padding: 0 3rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 100%);
  position: relative;
  z-index: 2;
}

/* Add professional content overlay patterns */
.hero-content-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
  z-index: 1;
}

/* Subtle animated accent lines */
.hero-content-modern::after {
  content: '';
  position: absolute;
  top: 20%;
  left: 2rem;
  width: 4px;
  height: 60%;
  background: linear-gradient(to bottom,
    transparent 0%,
    rgba(255, 215, 0, 0.8) 20%,
    rgba(255, 255, 255, 0.6) 50%,
    rgba(255, 215, 0, 0.8) 80%,
    transparent 100%);
  border-radius: 2px;
  animation: accentPulse 3s ease-in-out infinite;
  z-index: 1;
}

@keyframes accentPulse {
  0%, 100% { opacity: 0.6; transform: scaleY(1); }
  50% { opacity: 1; transform: scaleY(1.1); }
}

.hero-content-inner {
  position: relative;
  z-index: 2;
  max-width: 600px;
}

/* Event Status Badge */
.event-status-modern {
  margin-bottom: 2rem;
}

.status-badge {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 1rem 1.5rem;
  max-width: fit-content;
  transition: all 0.3s ease;
}

.status-badge:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.status-badge.upcoming {
  border-left: 4px solid #ffc107;
}

.status-badge.live {
  border-left: 4px solid #28a745;
  animation: pulse 2s infinite;
}

.status-badge.past {
  border-left: 4px solid #6c757d;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: white;
  font-size: 1.1rem;
}

.status-text {
  display: flex;
  flex-direction: column;
}

.status-label {
  color: white;
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.status-date {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
}

/* Title Section */
.hero-title-modern {
  margin-bottom: 1.5rem;
}

.hero-title-modern h1 {
  font-size: 3.5rem;
  font-weight: 800;
  color: white;
  line-height: 1.1;
  margin-bottom: 1rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.title-decoration {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #ffc107, #fd7e14);
  border-radius: 2px;
  position: relative;
}

.title-decoration::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 4px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 2px;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-40px); }
  100% { transform: translateX(120px); }
}

.hero-subtitle-modern {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  margin-bottom: 1.5rem;
  line-height: 1.4;
}

.hero-description-modern {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2.5rem;
}

/* Stats Section */
.hero-stats-modern {
  display: flex;
  gap: 2rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.stat-item-modern {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1rem 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-item-modern:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: white;
  font-size: 1.2rem;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 0.25rem;
}

/* Action Buttons */
.hero-actions-modern {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-modern {
  display: inline-flex;
  align-items: center;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-primary-modern {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: #212529;
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
}

.btn-primary-modern:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 193, 7, 0.6);
  color: #212529;
}

.btn-secondary-modern {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary-modern:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
  color: white;
}

.btn-icon {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.btn-text {
  position: relative;
  z-index: 2;
}

/* Right Image Section */
.hero-image-modern {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
  z-index: 2;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.8s ease;
}

.hero-slide-modern.active .hero-image {
  animation: imageZoom 0.8s ease-out;
}

@keyframes imageZoom {
  from {
    transform: scale(1.1);
  }
  to {
    transform: scale(1);
  }
}

/* Decorative Elements */
.image-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.circle-1 {
  width: 120px;
  height: 120px;
  top: 10%;
  right: 10%;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  right: 20%;
  animation: float 4s ease-in-out infinite reverse;
}

.circle-3 {
  width: 60px;
  height: 60px;
  top: 50%;
  right: 5%;
  animation: float 5s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Responsive Design for Modern Hero */
@media (max-width: 991.98px) {
  .hero-slide-modern {
    min-height: auto;
  }

  .hero-content-modern {
    height: auto;
    padding: 3rem 2rem;
  }

  .hero-image-modern {
    height: 50vh;
    order: -1;
  }

  .hero-title-modern h1 {
    font-size: 2.5rem;
  }

  .hero-stats-modern {
    gap: 1rem;
  }

  .stat-item-modern {
    flex: 1;
    min-width: 200px;
  }

  .hero-actions-modern {
    justify-content: center;
  }
}

@media (max-width: 767.98px) {
  .hero-content-modern {
    padding: 2rem 1rem;
  }

  .hero-title-modern h1 {
    font-size: 2rem;
  }

  .hero-subtitle-modern {
    font-size: 1.25rem;
  }

  .hero-description-modern {
    font-size: 1rem;
  }

  .hero-stats-modern {
    flex-direction: column;
    gap: 0.75rem;
  }

  .stat-item-modern {
    min-width: auto;
  }

  .btn-modern {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* Gallery Section Styles */
.gallery-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.gallery-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="gallery-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(102,126,234,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23gallery-pattern)"/></svg>');
  opacity: 0.5;
  z-index: 1;
}

.gallery-section .container {
  position: relative;
  z-index: 2;
}

.gallery-grid {
  margin-top: 60px;
}

.gallery-item {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  background: white;
  height: 300px;
}

.gallery-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
}

.gallery-item:first-child {
  height: 400px;
}

.gallery-image {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.gallery-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.4s ease;
}

.gallery-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  transition: all 0.4s ease;
}

.gallery-placeholder i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.9;
  transition: all 0.4s ease;
}

.gallery-placeholder span {
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.8;
}

.gallery-item:hover .gallery-placeholder {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.gallery-item:hover .gallery-placeholder i {
  transform: scale(1.2);
  opacity: 1;
}

.gallery-item:hover .gallery-image img {
  transform: scale(1.1);
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(102, 126, 234, 0.9) 0%,
    rgba(118, 75, 162, 0.9) 100%
  );
  opacity: 0;
  transition: all 0.4s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-content {
  color: white;
  transform: translateY(20px);
  transition: transform 0.4s ease;
}

.gallery-item:hover .gallery-content {
  transform: translateY(0);
}

.gallery-content h4 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.gallery-content p {
  font-size: 1rem;
  margin-bottom: 1rem;
  opacity: 0.9;
  line-height: 1.5;
}

.gallery-category {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Gallery Button */
.gallery-section .btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50px;
  padding: 1rem 2.5rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.gallery-section .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.gallery-section .btn:hover::before {
  left: 100%;
}

.gallery-section .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

/* Responsive Gallery */
@media (max-width: 991.98px) {
  .gallery-section {
    padding: 80px 0;
  }

  .gallery-item,
  .gallery-item:first-child {
    height: 250px;
    margin-bottom: 1.5rem;
  }

  .gallery-content h4 {
    font-size: 1.25rem;
  }

  .gallery-content p {
    font-size: 0.9rem;
  }
}

@media (max-width: 767.98px) {
  .gallery-section {
    padding: 60px 0;
  }

  .gallery-grid {
    margin-top: 40px;
  }

  .gallery-item,
  .gallery-item:first-child {
    height: 200px;
    margin-bottom: 1rem;
  }

  .gallery-overlay {
    padding: 1rem;
  }

  .gallery-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .gallery-content p {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
  }

  .gallery-category {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
  }
}

/* Modern Navigation Styles */
.modern-navbar {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.modern-navbar.scrolled {
  background: rgba(255, 255, 255, 0.98) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.brand-logo {
  transition: all 0.3s ease;
  text-decoration: none !important;
}

.brand-logo:hover {
  transform: translateY(-2px);
}

.brand-image {
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
}

.brand-logo:hover .brand-image {
  transform: scale(1.05);
}

.brand-icon-wrapper {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.brand-title {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.brand-subtitle {
  color: #6c757d;
  font-weight: 500;
}

.modern-toggler {
  border: none;
  padding: 4px;
  width: 30px;
  height: 30px;
  position: relative;
  background: transparent;
}

.modern-toggler span {
  display: block;
  width: 20px;
  height: 2px;
  background: #667eea;
  margin: 4px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.modern-toggler:hover span {
  background: #764ba2;
}

.modern-nav {
  align-items: center;
  gap: 0.25rem;
  flex: 0 0 auto;
  max-width: 450px;
}

.modern-nav-link {
  position: relative;
  padding: 10px 12px !important;
  margin: 0 2px;
  border-radius: 8px;
  transition: all 0.3s ease;
  color: #495057 !important;
  font-weight: 500;
  text-decoration: none !important;
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.modern-nav-link:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea !important;
  transform: translateY(-2px);
}

.modern-nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.modern-nav-link i {
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.modern-nav-link:hover i {
  transform: scale(1.1);
}

.modern-cta-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 25px;
  padding: 10px 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.modern-cta-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.modern-cta-btn:hover::before {
  left: 100%;
}

.modern-cta-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Mobile Navigation */
@media (max-width: 991.98px) {
  .modern-navbar .navbar-collapse {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    margin-top: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .modern-nav-link {
    margin: 4px 0;
    justify-content: flex-start;
  }

  .modern-cta-btn {
    margin-top: 15px;
    width: 100%;
    justify-content: center;
  }
}

/* Layout Fixes for Content Cutoff */
.footer-section {
  margin-top: auto;
  position: relative;
  z-index: 10;
}

/* Ensure proper spacing for all page content */
.main-content-wrapper {
  min-height: calc(100vh - 200px);
  padding-bottom: 100px;
}

/* Main content spacing for no-hero pages */
.main-content-modern.no-hero {
  /* padding-top: 100px; */
}

/* Event detail and registration page fixes */
.event-detail-container,
.registration-container {
  padding-bottom: 80px !important;
  margin-bottom: 40px;
  min-height: auto !important;
}

/* Hero content spacing fixes */
.hero-content-wrapper {
  padding-bottom: 80px !important;
  margin-bottom: 80px !important;
  min-height: auto !important;
}

.content-card {
  margin-bottom: 80px !important;
  min-height: auto !important;
  overflow: visible !important;
}

/* Fix for dynamic content pages */
.hero-section {
  min-height: auto !important;
  height: auto !important;
  overflow: visible !important;
}

/* Ensure proper spacing for all dynamic content */
.register-page,
.event-detail-page {
  padding-bottom: 80px !important;
  min-height: auto !important;
}

/* Responsive spacing adjustments */
@media (max-width: 768px) {
  .event-detail-container,
  .registration-container {
    padding-bottom: 60px !important;
    margin-bottom: 40px;
  }

  .hero-content-wrapper {
    padding-bottom: 60px !important;
    margin-bottom: 60px !important;
  }

  .content-card {
    margin-bottom: 60px !important;
  }

  .register-page,
  .event-detail-page {
    padding-bottom: 60px !important;
  }
}

/* Fix for clickability and overlapping issues */
.hero-section,
.main-content-wrapper,
.content-card {
  position: relative;
  z-index: 1;
}

/* Ensure all interactive elements are clickable */
button, a, input, select, textarea {
  position: relative;
  z-index: 2;
}

/* Fix for any overlay issues */
.hero-content-wrapper .content-card {
  position: relative;
  z-index: 10;
}

/* Prevent any background elements from interfering */
.hero-section::before,
.hero-section::after {
  pointer-events: none;
}
