#!/usr/bin/env python
"""
Script to add sample images for Gondar City attractions.
This script creates database entries for the attractions with proper metadata.
"""

import os
import sys
import django
from datetime import date

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from gallery.models import GalleryCategory, GalleryImage
from django.core.files.base import ContentFile
import requests
from io import BytesIO
from PIL import Image

def download_and_save_image(url, filename):
    """Download image from URL and return a Django file object"""
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # Create a PIL image to ensure it's valid
        img = Image.open(BytesIO(response.content))
        
        # Convert to RGB if necessary (for JPEG compatibility)
        if img.mode in ('RGBA', 'LA', 'P'):
            img = img.convert('RGB')
        
        # Save to BytesIO
        img_io = BytesIO()
        img.save(img_io, format='JPEG', quality=85)
        img_io.seek(0)
        
        return ContentFile(img_io.read(), name=filename)
    except Exception as e:
        print(f"   ⚠ Failed to download {url}: {e}")
        return None

def add_gondar_sample_images():
    """Add sample images for Gondar City attractions"""
    
    print("Adding sample images for Gondar City attractions...")
    
    # Get the categories
    try:
        fasil_category = GalleryCategory.objects.get(slug='fasil-ghebbi')
        debre_category = GalleryCategory.objects.get(slug='debre-berhan-selassie')
        fasilides_category = GalleryCategory.objects.get(slug='fasilides-bath')
        gondar_category = GalleryCategory.objects.get(slug='gondar-city')
    except GalleryCategory.DoesNotExist as e:
        print(f"Error: Required category not found: {e}")
        return
    
    # Define sample images with their original URLs for reference
    sample_images = [
        {
            'title': 'Fasil Ghebbi Royal Enclosure',
            'description': 'The royal enclosure containing six castles and several other buildings, showcasing unique Ethiopian architecture.',
            'category': fasil_category,
            'url': 'https://i0.wp.com/thinkafrica.net/wp-content/uploads/2019/02/fasil-ghebbi-featued-gondar10.jpg?resize=780%2C278&ssl=1',
            'filename': 'fasil_ghebbi_royal_enclosure.jpg',
            'alt_text': 'Fasil Ghebbi Royal Enclosure showing ancient Ethiopian castle architecture',
            'location': 'Gondar, Ethiopia',
            'tags': 'royal, castle, unesco, heritage, architecture, ethiopia',
            'is_featured': True,
        },
        {
            'title': 'Debre Berhan Selassie Church',
            'description': 'Famous church known for its beautiful ceiling decorated with angelic faces and religious paintings.',
            'category': debre_category,
            'url': 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSK44gzJXIJoVNnlM4nMeEAI4UIzi3bAtggsg&s',
            'filename': 'debre_berhan_selassie_church.jpg',
            'alt_text': 'Debre Berhan Selassie Church with its famous angelic ceiling paintings',
            'location': 'Gondar, Ethiopia',
            'tags': 'church, religious, art, culture, angels, paintings',
            'is_featured': True,
        },
        {
            'title': 'Fasilides Bath Historic Pool',
            'description': 'Historic bathing complex used for the annual Timkat (Epiphany) celebrations, surrounded by beautiful gardens.',
            'category': fasilides_category,
            'url': 'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEj5laguI2fO9Q8qFEowdNsETtW__a2rFAxmQxUiqz0YaTlKJ-5El8g5qGDV_8uHcHq7_mcxvbtWlYkBvsE4KZ9Ro0LB3w2C4ns6NzcQDDaeMF0dWZXxh1W-tSlhCKsPa2tz4vpLYoyHQpSl/s1600/fasiledas+bath-1.jpg',
            'filename': 'fasilides_bath_historic_pool.jpg',
            'alt_text': 'Fasilides Bath historic bathing complex used for Timkat celebrations',
            'location': 'Gondar, Ethiopia',
            'tags': 'bath, timkat, epiphany, celebration, historic, water',
            'is_featured': True,
        },
    ]
    
    created_count = 0
    skipped_count = 0
    
    for img_data in sample_images:
        try:
            # Check if image already exists
            existing_image = GalleryImage.objects.filter(
                title=img_data['title'],
                category=img_data['category']
            ).first()
            
            if existing_image:
                print(f"   • Image already exists: {img_data['title']}")
                skipped_count += 1
                continue
            
            print(f"   📥 Downloading: {img_data['title']}")
            
            # Download and save the image
            image_file = download_and_save_image(img_data['url'], img_data['filename'])
            
            if image_file:
                # Create the gallery image
                gallery_image = GalleryImage.objects.create(
                    title=img_data['title'],
                    description=img_data['description'],
                    image=image_file,
                    category=img_data['category'],
                    tags=img_data['tags'],
                    alt_text=img_data['alt_text'],
                    location=img_data['location'],
                    is_featured=img_data.get('is_featured', False),
                    is_active=True,
                    date_taken=date.today(),
                    photographer='University of Gondar',
                    order=created_count + 1
                )
                
                created_count += 1
                print(f"   ✓ Created image: {gallery_image.title}")
            else:
                print(f"   ✗ Failed to create image: {img_data['title']}")
                
        except Exception as e:
            print(f"   ✗ Error creating image {img_data['title']}: {e}")
    
    print(f"\nGondar sample images setup complete!")
    print(f"Created: {created_count} new images")
    print(f"Skipped: {skipped_count} existing images")
    
    # Display summary
    print("\nCurrent Gondar attraction images:")
    for category in [fasil_category, debre_category, fasilides_category]:
        images = GalleryImage.objects.filter(category=category, is_active=True)
        print(f"   - {category.name}: {images.count()} images")
        for img in images:
            print(f"     • {img.title}")

if __name__ == '__main__':
    add_gondar_sample_images()
